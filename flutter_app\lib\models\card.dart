// Simplified version without code generation
// import 'package:json_annotation/json_annotation.dart';
// part 'card.g.dart';
// @JsonSerializable()
class GameCard {
  final int id;
  final String name;
  final String type;
  final String rarity;
  final Map<String, dynamic> stats;
  final Map<String, dynamic>? abilities;
  final String? imageUrl;
  final String? description;
  final String? flavorText;
  final bool isActive;
  final DateTime? createdAt;

  const GameCard({
    required this.id,
    required this.name,
    required this.type,
    required this.rarity,
    required this.stats,
    this.abilities,
    this.imageUrl,
    this.description,
    this.flavorText,
    this.isActive = true,
    this.createdAt,
  });

  // Simplified without code generation
  factory GameCard.fromJson(Map<String, dynamic> json) {
    return GameCard(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      rarity: json['rarity'] ?? 'common',
      stats: json['stats'] ?? {},
      abilities: json['abilities'],
      imageUrl: json['image_url'],
      description: json['description'],
      flavorText: json['flavor_text'],
      isActive: json['is_active'] ?? true,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'rarity': rarity,
      'stats': stats,
      'abilities': abilities,
      'image_url': imageUrl,
      'description': description,
      'flavor_text': flavorText,
      'is_active': isActive,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  // Get stat value by name
  int getStatValue(String statName) {
    return (stats[statName] as num?)?.toInt() ?? 0;
  }

  // Get ability value by name
  dynamic getAbilityValue(String abilityName) {
    return abilities?[abilityName];
  }

  // Check if card has specific ability
  bool hasAbility(String abilityName) {
    return abilities?.containsKey(abilityName) ?? false;
  }

  // Get primary stats based on card type
  List<CardStat> get primaryStats {
    switch (type.toLowerCase()) {
      case 'driver':
        return [
          CardStat('Speed', getStatValue('speed')),
          CardStat('Handling', getStatValue('handling')),
          CardStat('Consistency', getStatValue('consistency')),
          CardStat('Adaptability', getStatValue('adaptability')),
        ];
      case 'codriver':
        return [
          CardStat('Pace Notes', getStatValue('pace_notes')),
          CardStat('Route Knowledge', getStatValue('route_knowledge')),
          CardStat('Communication', getStatValue('communication')),
          CardStat('Experience', getStatValue('experience')),
        ];
      case 'vehicle':
        return [
          CardStat('Top Speed', getStatValue('top_speed')),
          CardStat('Acceleration', getStatValue('acceleration')),
          CardStat('Handling', getStatValue('handling')),
          CardStat('Reliability', getStatValue('reliability')),
        ];
      case 'strategy':
        return [
          if (getStatValue('speed_bonus') != 0)
            CardStat('Speed Bonus', getStatValue('speed_bonus')),
          if (getStatValue('crash_risk') != 0)
            CardStat('Crash Risk', getStatValue('crash_risk')),
        ];
      default:
        return [];
    }
  }

  // Get overall rating (average of primary stats)
  int get overallRating {
    final stats = primaryStats;
    if (stats.isEmpty) return 0;
    final total = stats.fold<int>(0, (sum, stat) => sum + stat.value);
    return (total / stats.length).round();
  }

  @override
  String toString() {
    return 'GameCard(id: $id, name: $name, type: $type, rarity: $rarity)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GameCard && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

@JsonSerializable()
class PlayerCard {
  @JsonKey(name: 'player_card_id')
  final int playerCardId;
  @JsonKey(name: 'player_id')
  final int playerId;
  @JsonKey(name: 'card_id')
  final int cardId;
  final int level;
  final int experience;
  @JsonKey(name: 'obtained_at')
  final DateTime obtainedAt;
  @JsonKey(name: 'is_favorite')
  final bool isFavorite;
  final GameCard? card;
  @JsonKey(name: 'effective_stats')
  final Map<String, dynamic>? effectiveStats;

  const PlayerCard({
    required this.playerCardId,
    required this.playerId,
    required this.cardId,
    required this.level,
    required this.experience,
    required this.obtainedAt,
    required this.isFavorite,
    this.card,
    this.effectiveStats,
  });

  factory PlayerCard.fromJson(Map<String, dynamic> json) => _$PlayerCardFromJson(json);
  Map<String, dynamic> toJson() => _$PlayerCardToJson(this);

  // Calculate experience needed for next level
  int get experienceToNextLevel {
    final nextLevelExp = level * 100; // Simple formula
    return nextLevelExp - experience;
  }

  // Calculate experience progress percentage
  double get experienceProgress {
    final currentLevelExp = (level - 1) * 100;
    final nextLevelExp = level * 100;
    final progressExp = experience - currentLevelExp;
    final totalExpNeeded = nextLevelExp - currentLevelExp;
    return progressExp / totalExpNeeded;
  }

  // Get effective stat value (with level bonus)
  int getEffectiveStatValue(String statName) {
    if (effectiveStats != null) {
      return (effectiveStats![statName] as num?)?.toInt() ?? 0;
    }
    // Fallback calculation
    final baseStat = card?.getStatValue(statName) ?? 0;
    final levelBonus = (level - 1) * 0.05; // 5% per level
    return (baseStat * (1 + levelBonus)).round();
  }

  // Get upgrade cost
  int get upgradeCost {
    return level * 100; // Simple formula
  }

  PlayerCard copyWith({
    int? playerCardId,
    int? playerId,
    int? cardId,
    int? level,
    int? experience,
    DateTime? obtainedAt,
    bool? isFavorite,
    GameCard? card,
    Map<String, dynamic>? effectiveStats,
  }) {
    return PlayerCard(
      playerCardId: playerCardId ?? this.playerCardId,
      playerId: playerId ?? this.playerId,
      cardId: cardId ?? this.cardId,
      level: level ?? this.level,
      experience: experience ?? this.experience,
      obtainedAt: obtainedAt ?? this.obtainedAt,
      isFavorite: isFavorite ?? this.isFavorite,
      card: card ?? this.card,
      effectiveStats: effectiveStats ?? this.effectiveStats,
    );
  }

  @override
  String toString() {
    return 'PlayerCard(id: $playerCardId, cardId: $cardId, level: $level)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PlayerCard && other.playerCardId == playerCardId;
  }

  @override
  int get hashCode => playerCardId.hashCode;
}

@JsonSerializable()
class CardStat {
  final String name;
  final int value;

  const CardStat(this.name, this.value);

  factory CardStat.fromJson(Map<String, dynamic> json) => _$CardStatFromJson(json);
  Map<String, dynamic> toJson() => _$CardStatToJson(this);

  @override
  String toString() => '$name: $value';
}

@JsonSerializable()
class Deck {
  final int? id;
  final String name;
  @JsonKey(name: 'driver_card_id')
  final int driverCardId;
  @JsonKey(name: 'codriver_card_id')
  final int codriverCardId;
  @JsonKey(name: 'vehicle_card_id')
  final int vehicleCardId;
  @JsonKey(name: 'strategy_cards')
  final List<int> strategyCards;
  @JsonKey(name: 'is_active')
  final bool isActive;
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;

  const Deck({
    this.id,
    required this.name,
    required this.driverCardId,
    required this.codriverCardId,
    required this.vehicleCardId,
    required this.strategyCards,
    this.isActive = true,
    this.createdAt,
  });

  factory Deck.fromJson(Map<String, dynamic> json) => _$DeckFromJson(json);
  Map<String, dynamic> toJson() => _$DeckToJson(this);

  // Validate deck configuration
  bool get isValid {
    return driverCardId > 0 &&
           codriverCardId > 0 &&
           vehicleCardId > 0 &&
           strategyCards.length >= 3 &&
           strategyCards.length <= 5;
  }

  Deck copyWith({
    int? id,
    String? name,
    int? driverCardId,
    int? codriverCardId,
    int? vehicleCardId,
    List<int>? strategyCards,
    bool? isActive,
    DateTime? createdAt,
  }) {
    return Deck(
      id: id ?? this.id,
      name: name ?? this.name,
      driverCardId: driverCardId ?? this.driverCardId,
      codriverCardId: codriverCardId ?? this.codriverCardId,
      vehicleCardId: vehicleCardId ?? this.vehicleCardId,
      strategyCards: strategyCards ?? this.strategyCards,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'Deck(id: $id, name: $name, valid: $isValid)';
  }
}
